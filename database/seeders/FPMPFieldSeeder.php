<?php

namespace Database\Seeders;

use App\Models\Amenity;
use App\Models\Field;
use Illuminate\Database\Seeder;

class FPMPFieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fpmpFields = [
            [
                'name' => 'FPMP Soccer Field',
                'type' => 'Soccer',
                'description' => 'Professional soccer field at FPMP Sport Park Mariepampoen with natural grass surface and professional lighting.',
                'hourly_rate' => 75.00,
                'night_hourly_rate' => 90.00,
                'night_time_start' => '18:00',
                'capacity' => 22,
                'status' => 'Active',
                'amenities' => ['Lighting', 'Parking', 'Restrooms', 'Equipment Available', 'Scoreboard', 'Spectator Seating'],
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1.0,
                'max_booking_hours' => 8.0,
            ],
            [
                'name' => 'FPMP Multi-Purpose Field',
                'type' => 'Multi-Purpose',
                'description' => 'Versatile multi-purpose field suitable for various sports activities including football, rugby, and training sessions.',
                'hourly_rate' => 60.00,
                'night_hourly_rate' => 70.00,
                'night_time_start' => '18:00',
                'capacity' => 30,
                'status' => 'Active',
                'amenities' => ['Lighting', 'Parking', 'Restrooms', 'Equipment Available'],
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1.0,
                'max_booking_hours' => 8.0,
            ],
            [
                'name' => 'FPMP Bolas Field',
                'type' => 'Multi-Purpose', // Using existing type for now
                'description' => 'Traditional bolas field for local sports and recreational activities with authentic playing surface.',
                'hourly_rate' => 45.00,
                'night_hourly_rate' => 55.00,
                'night_time_start' => '18:00',
                'capacity' => 16,
                'status' => 'Active',
                'amenities' => ['Lighting', 'Parking', 'Restrooms'],
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1.0,
                'max_booking_hours' => 6.0,
            ],
            [
                'name' => 'FPMP Patio Area',
                'type' => 'Multi-Purpose', // Using existing type for now
                'description' => 'Covered patio area perfect for events, parties, and social gatherings with modern amenities.',
                'hourly_rate' => 35.00,
                'capacity' => 50,
                'status' => 'Active',
                'amenities' => ['Parking', 'Restrooms', 'Spectator Seating', 'Sound System', 'WiFi'],
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 2,
                'max_booking_hours' => 8,
            ],
        ];

        foreach ($fpmpFields as $fieldData) {
            // Check if field already exists to avoid duplicates
            $existingField = Field::where('name', $fieldData['name'])->first();

            if (! $existingField) {
                // Extract amenities from field data
                $amenityNames = $fieldData['amenities'];
                unset($fieldData['amenities']);

                // Create the field
                $field = Field::create($fieldData);

                // Attach amenities using the relationship
                if (! empty($amenityNames)) {
                    $amenityIds = Amenity::whereIn('name', $amenityNames)->pluck('id')->toArray();
                    if (! empty($amenityIds)) {
                        $field->amenities()->attach($amenityIds);
                    }
                }

                $this->command->info("Created field: {$field->name} with ".count($amenityIds ?? []).' amenities');
            } else {
                $this->command->info("Field already exists: {$fieldData['name']}");
            }
        }

        $this->command->info('FPMP fields seeding completed!');
    }
}
