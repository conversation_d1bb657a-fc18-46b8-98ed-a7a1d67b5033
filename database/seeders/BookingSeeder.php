<?php

namespace Database\Seeders;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class BookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fields = Field::all();
        $users = User::all();

        if ($fields->isEmpty() || $users->isEmpty()) {
            $this->command->info('No fields or users found. Please run FieldSeeder and UserRoleSeeder first.');

            return;
        }

        $bookings = [
            [
                'field_id' => $fields->where('name', 'Soccer Field A')->first()?->id ?? $fields->first()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->first()->id,
                'booking_date' => Carbon::today()->addDays(1),
                'start_time' => '09:00',
                'end_time' => '11:00',
                'duration_hours' => 2,
                'total_cost' => 150.00,
                'status' => 'Confirmed',
                'customer_name' => '<PERSON>',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '555-0123',
                'special_requests' => 'Need equipment setup',
                'confirmed_at' => now(),
            ],
            [
                'field_id' => $fields->where('name', 'Basketball Court 1')->first()?->id ?? $fields->skip(1)->first()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->skip(1)->first()->id,
                'booking_date' => Carbon::today()->addDays(2),
                'start_time' => '14:00',
                'end_time' => '16:00',
                'duration_hours' => 2,
                'total_cost' => 90.00,
                'status' => 'Pending',
                'customer_name' => 'Jane Doe',
                'customer_email' => '<EMAIL>',
                'special_requests' => 'Birthday party setup',
            ],
            [
                'field_id' => $fields->where('name', 'Tennis Court A')->first()?->id ?? $fields->skip(2)->first()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->first()->id,
                'booking_date' => Carbon::today()->addDays(3),
                'start_time' => '10:00',
                'end_time' => '12:00',
                'duration_hours' => 2,
                'total_cost' => 70.00,
                'status' => 'Confirmed',
                'confirmed_at' => now(),
            ],
            [
                'field_id' => $fields->where('name', 'Soccer Field A')->first()?->id ?? $fields->first()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->first()->id,
                'booked_by' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->first()->id,
                'booking_date' => Carbon::today()->addDays(5),
                'start_time' => '16:00',
                'end_time' => '18:00',
                'duration_hours' => 2,
                'total_cost' => 150.00,
                'status' => 'Confirmed',
                'customer_name' => 'Corporate Event',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '555-0199',
                'special_requests' => 'Corporate team building event',
                'admin_notes' => 'VIP booking - ensure premium setup',
                'confirmed_at' => now(),
            ],
            [
                'field_id' => $fields->where('name', 'Multi-Purpose Field')->first()?->id ?? $fields->last()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->skip(1)->first()->id,
                'booking_date' => Carbon::today()->subDays(1),
                'start_time' => '13:00',
                'end_time' => '15:00',
                'duration_hours' => 2,
                'total_cost' => 110.00,
                'status' => 'Completed',
                'customer_name' => 'Youth Soccer League',
                'customer_email' => '<EMAIL>',
                'special_requests' => 'Youth tournament',
                'confirmed_at' => now()->subDays(2),
            ],
            [
                'field_id' => $fields->where('name', 'Basketball Court 1')->first()?->id ?? $fields->skip(1)->first()->id,
                'user_id' => $users->where('email', '<EMAIL>')->first()?->id ?? $users->first()->id,
                'booking_date' => Carbon::today()->addDays(7),
                'start_time' => '19:00',
                'end_time' => '21:00',
                'duration_hours' => 2,
                'total_cost' => 90.00,
                'status' => 'Confirmed',
                'special_requests' => 'Evening basketball practice',
                'confirmed_at' => now(),
            ],
        ];

        foreach ($bookings as $bookingData) {
            Booking::create($bookingData);
        }

        $this->command->info('Sample bookings created successfully!');
    }
}
