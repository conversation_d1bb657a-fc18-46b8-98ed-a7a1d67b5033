<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Call the UserRoleSeeder to create test users with different roles
        $this->call([
            UserRoleSeeder::class,
            AmenitySeeder::class,  // Must run before FieldSeeder
            UtilitySeeder::class,  // Must run before FieldSeeder
            FieldSeeder::class,
            FPMPFieldSeeder::class,
            BookingSeeder::class,
        ]);
    }
}
