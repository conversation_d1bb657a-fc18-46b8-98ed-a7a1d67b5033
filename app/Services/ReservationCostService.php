<?php

namespace App\Services;

use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;

class ReservationCostService
{
    /**
     * Calculate the total cost for a reservation using sophisticated day/night rate logic (supports half-hour increments)
     */
    public function calculateTotalCost(Field $field, float $durationHours, ?string $startTime = null): float
    {
        if ($startTime) {
            // Use sophisticated calculation with day/night rates
            $startCarbon = \Carbon\Carbon::createFromFormat('H:i', $startTime);

            // Calculate end time using decimal hours (supports half-hour increments)
            $totalMinutes = $durationHours * 60;
            $endCarbon = $startCarbon->copy()->addMinutes($totalMinutes);
            $endTime = $endCarbon->format('H:i');

            return $field->calculateBookingCost($startTime, $endTime);
        }

        // Fallback to simple calculation for backward compatibility
        // If no start time provided, assume day rate for entire duration
        return $field->hourly_rate * $durationHours;
    }

    /**
     * Calculate total cost including utilities (supports half-hour increments)
     */
    public function calculateTotalCostWithUtilities(Field $field, float $durationHours, ?string $startTime = null, array $utilities = []): array
    {
        // Calculate base field cost
        $fieldCost = $this->calculateTotalCost($field, $durationHours, $startTime);

        // Calculate utility costs (optimized to avoid N+1 queries)
        $utilityCost = 0;
        $utilityBreakdown = [];

        if (! empty($utilities)) {
            // Bulk load all utilities to avoid N+1 queries
            $utilityIds = array_column($utilities, 'id');
            $utilityModels = \App\Models\Utility::whereIn('id', $utilityIds)->get()->keyBy('id');

            foreach ($utilities as $utility) {
                $utilityModel = $utilityModels->get($utility['id']);
                if ($utilityModel) {
                    // Utilities are now whole number quantities, not hours
                    $utilityQuantity = (int) $utility['hours']; // Keep 'hours' key for backward compatibility
                    $cost = $utilityModel->hourly_rate * $utilityQuantity;
                    $utilityCost += $cost;

                    $utilityBreakdown[] = [
                        'utility_id' => $utilityModel->id,
                        'name' => $utilityModel->name,
                        'hours' => $utilityQuantity, // Keep 'hours' key for backward compatibility
                        'rate' => $utilityModel->hourly_rate,
                        'cost' => round($cost, 2),
                    ];
                }
            }
        }

        return [
            'field_cost' => round($fieldCost, 2),
            'utility_cost' => round($utilityCost, 2),
            'total_cost' => round($fieldCost + $utilityCost, 2),
            'utility_breakdown' => $utilityBreakdown,
        ];
    }

    /**
     * Get detailed cost breakdown for a reservation (supports half-hour increments)
     */
    public function getCostBreakdown(Field $field, float $durationHours, ?string $startTime = null): array
    {
        $hourlyRate = $field->hourly_rate;
        $totalCost = $this->calculateTotalCost($field, $durationHours, $startTime);

        // Determine if night rates apply
        $hasNightRates = $field->night_hourly_rate && $field->night_time_start;
        $nightRate = $hasNightRates ? $field->night_hourly_rate : null;

        // Calculate detailed day/night breakdown if start time is provided
        $rateBreakdown = [];
        if ($startTime && $hasNightRates) {
            $rateBreakdown = $this->calculateDayNightBreakdown($field, $durationHours, $startTime);
        }

        return [
            'field_name' => $field->name,
            'hourly_rate' => $hourlyRate,
            'night_hourly_rate' => $nightRate,
            'duration_hours' => $durationHours,
            'start_time' => $startTime,
            'subtotal' => $totalCost,
            'tax_rate' => 0, // No tax for Phase 1
            'tax_amount' => 0,
            'total_cost' => $totalCost,
            'has_night_rates' => $hasNightRates,
            'rate_breakdown' => $rateBreakdown,
            'formatted' => [
                'hourly_rate' => '$'.number_format($hourlyRate, 2),
                'night_hourly_rate' => $nightRate ? '$'.number_format($nightRate, 2) : null,
                'subtotal' => '$'.number_format($totalCost, 2),
                'tax_amount' => '$0.00',
                'total_cost' => '$'.number_format($totalCost, 2),
            ],
        ];
    }

    /**
     * Calculate detailed day/night rate breakdown
     */
    private function calculateDayNightBreakdown(Field $field, float $durationHours, string $startTime): array
    {
        if (! $field->night_hourly_rate || ! $field->night_time_start) {
            return [
                'day_hours' => $durationHours,
                'night_hours' => 0,
                'day_cost' => $field->hourly_rate * $durationHours,
                'night_cost' => 0,
            ];
        }

        $start = \Carbon\Carbon::createFromFormat('H:i', $startTime);
        $end = $start->copy()->addMinutes($durationHours * 60);

        // Handle midnight crossing
        if ($end->lt($start)) {
            $end->addDay();
        }

        $dayHours = 0;
        $nightHours = 0;
        $current = $start->copy();

        // Calculate in 30-minute increments for accurate half-hour support
        while ($current->lt($end)) {
            $timeForRate = $current->format('H:i');
            $segmentEnd = $current->copy()->addMinutes(30);
            if ($segmentEnd->gt($end)) {
                $segmentEnd = $end->copy();
            }

            $segmentMinutes = $current->diffInMinutes($segmentEnd);
            $segmentHours = $segmentMinutes / 60;

            if ($field->isNightTime($timeForRate)) {
                $nightHours += $segmentHours;
            } else {
                $dayHours += $segmentHours;
            }
            $current->addMinutes(30);
        }

        return [
            'day_hours' => $dayHours,
            'night_hours' => $nightHours,
            'day_cost' => $dayHours * $field->hourly_rate,
            'night_cost' => $nightHours * $field->night_hourly_rate,
        ];
    }

    /**
     * Calculate cost for multiple time slots
     */
    public function calculateMultiSlotCost(array $slots): array
    {
        $totalCost = 0;
        $breakdown = [];

        foreach ($slots as $slot) {
            $field = Field::find($slot['field_id']);
            $duration = $slot['duration_hours'];
            $cost = $this->calculateTotalCost($field, $duration);

            $totalCost += $cost;
            $breakdown[] = [
                'field' => $field->name,
                'duration' => $duration,
                'cost' => $cost,
                'formatted_cost' => '$'.number_format($cost, 2),
            ];
        }

        return [
            'slots' => $breakdown,
            'total_cost' => $totalCost,
            'formatted_total' => '$'.number_format($totalCost, 2),
        ];
    }

    /**
     * Get cost comparison between different fields for same duration
     */
    public function getFieldCostComparison(float $durationHours): array
    {
        $fields = Field::where('status', 'Active')->orderBy('hourly_rate')->get();
        $comparison = [];

        foreach ($fields as $field) {
            $cost = $this->calculateTotalCost($field, $durationHours);
            $comparison[] = [
                'field' => $field,
                'cost' => $cost,
                'formatted_cost' => '$'.number_format($cost, 2),
                'cost_per_hour' => $field->hourly_rate,
                'formatted_hourly' => '$'.number_format($field->hourly_rate, 2),
            ];
        }

        return $comparison;
    }

    /**
     * Calculate potential savings for different durations
     */
    public function getDurationSavings(Field $field): array
    {
        $savings = [];
        $baseHourCost = $field->hourly_rate;

        for ($hours = $field->min_booking_hours; $hours <= $field->max_booking_hours; $hours += 0.5) {
            $totalCost = $this->calculateTotalCost($field, $hours);
            $costPerHour = $totalCost / $hours;

            $savings[] = [
                'duration' => $hours,
                'total_cost' => $totalCost,
                'cost_per_hour' => $costPerHour,
                'formatted_total' => '$'.number_format($totalCost, 2),
                'formatted_per_hour' => '$'.number_format($costPerHour, 2),
                'is_best_value' => $hours >= 3, // Longer bookings are better value
            ];
        }

        return $savings;
    }

    /**
     * Calculate monthly cost for recurring reservations
     */
    public function calculateMonthlyCost(Field $field, float $durationHours, int $timesPerWeek): array
    {
        $weeklyCost = $this->calculateTotalCost($field, $durationHours) * $timesPerWeek;
        $monthlyCost = $weeklyCost * 4.33; // Average weeks per month

        return [
            'single_session_cost' => $this->calculateTotalCost($field, $durationHours),
            'weekly_cost' => $weeklyCost,
            'monthly_cost' => $monthlyCost,
            'sessions_per_week' => $timesPerWeek,
            'sessions_per_month' => $timesPerWeek * 4.33,
            'formatted' => [
                'single_session' => '$'.number_format($this->calculateTotalCost($field, $durationHours), 2),
                'weekly' => '$'.number_format($weeklyCost, 2),
                'monthly' => '$'.number_format($monthlyCost, 2),
            ],
        ];
    }

    /**
     * Get cost statistics for a user's reservations
     */
    public function getUserCostStatistics(int $userId, ?Carbon $fromDate = null, ?Carbon $toDate = null): array
    {
        $fromDate = $fromDate ?: now()->startOfMonth();
        $toDate = $toDate ?: now()->endOfMonth();

        $reservations = Reservation::forUser($userId)
            ->whereBetween('booking_date', [$fromDate->format('Y-m-d'), $toDate->format('Y-m-d')])
            ->where('status', '!=', 'Cancelled')
            ->with('field')
            ->get();

        $totalCost = $reservations->sum('total_cost');
        $totalHours = $reservations->sum('duration_hours');
        $averageCostPerHour = $totalHours > 0 ? $totalCost / $totalHours : 0;
        $averageCostPerReservation = $reservations->count() > 0 ? $totalCost / $reservations->count() : 0;

        // Group by field
        $costByField = $reservations->groupBy('field_id')->map(function ($fieldReservations) {
            $field = $fieldReservations->first()->field;

            return [
                'field_name' => $field->name,
                'total_cost' => $fieldReservations->sum('total_cost'),
                'total_hours' => $fieldReservations->sum('duration_hours'),
                'reservation_count' => $fieldReservations->count(),
                'formatted_cost' => '$'.number_format($fieldReservations->sum('total_cost'), 2),
            ];
        });

        return [
            'period' => [
                'from' => $fromDate->format('M d, Y'),
                'to' => $toDate->format('M d, Y'),
            ],
            'totals' => [
                'total_cost' => $totalCost,
                'total_hours' => $totalHours,
                'total_reservations' => $reservations->count(),
                'average_cost_per_hour' => $averageCostPerHour,
                'average_cost_per_reservation' => $averageCostPerReservation,
            ],
            'formatted_totals' => [
                'total_cost' => '$'.number_format($totalCost, 2),
                'average_cost_per_hour' => '$'.number_format($averageCostPerHour, 2),
                'average_cost_per_reservation' => '$'.number_format($averageCostPerReservation, 2),
            ],
            'cost_by_field' => $costByField,
        ];
    }

    /**
     * Calculate cost for peak vs off-peak hours (future enhancement)
     */
    public function calculatePeakHourCost(Field $field, float $durationHours, string $startTime): array
    {
        // For Phase 1, all hours have the same rate
        // This method is prepared for future peak hour pricing

        $isPeakHour = $this->isPeakHour($startTime);
        $baseRate = $field->hourly_rate;
        $peakMultiplier = 1.0; // No peak pricing in Phase 1

        $effectiveRate = $isPeakHour ? $baseRate * $peakMultiplier : $baseRate;
        $totalCost = $effectiveRate * $durationHours;

        return [
            'is_peak_hour' => $isPeakHour,
            'base_rate' => $baseRate,
            'peak_multiplier' => $peakMultiplier,
            'effective_rate' => $effectiveRate,
            'total_cost' => $totalCost,
            'formatted' => [
                'base_rate' => '$'.number_format($baseRate, 2),
                'effective_rate' => '$'.number_format($effectiveRate, 2),
                'total_cost' => '$'.number_format($totalCost, 2),
            ],
        ];
    }

    /**
     * Check if a time is considered peak hour
     */
    private function isPeakHour(string $startTime): bool
    {
        $hour = (int) substr($startTime, 0, 2);

        // Define peak hours (6 PM - 9 PM for Phase 1)
        return $hour >= 18 && $hour < 21;
    }

    /**
     * Validate if cost calculation is correct
     */
    public function validateCostCalculation(Reservation $reservation): array
    {
        $expectedCost = $this->calculateTotalCost($reservation->field, $reservation->duration_hours);
        $actualCost = $reservation->total_cost;
        $isValid = abs($expectedCost - $actualCost) < 0.01; // Allow for small floating point differences

        return [
            'is_valid' => $isValid,
            'expected_cost' => $expectedCost,
            'actual_cost' => $actualCost,
            'difference' => $actualCost - $expectedCost,
            'formatted' => [
                'expected' => '$'.number_format($expectedCost, 2),
                'actual' => '$'.number_format($actualCost, 2),
                'difference' => '$'.number_format($actualCost - $expectedCost, 2),
            ],
        ];
    }

    /**
     * Get cost estimate for a potential reservation
     */
    public function getReservationEstimate(int $fieldId, float $durationHours, ?string $startTime = null, array $utilities = []): array
    {
        $field = Field::findOrFail($fieldId);
        $breakdown = $this->getCostBreakdown($field, $durationHours, $startTime);

        // Add utility costs if provided
        if (! empty($utilities)) {
            $costWithUtilities = $this->calculateTotalCostWithUtilities($field, $durationHours, $startTime, $utilities);
            $breakdown['field_cost'] = $costWithUtilities['field_cost'];
            $breakdown['utility_cost'] = $costWithUtilities['utility_cost'];
            $breakdown['total_cost'] = $costWithUtilities['total_cost'];
            $breakdown['utility_breakdown'] = $costWithUtilities['utility_breakdown'];
            $breakdown['formatted']['utility_cost'] = '$'.number_format($costWithUtilities['utility_cost'], 2);
            $breakdown['formatted']['total_cost'] = '$'.number_format($costWithUtilities['total_cost'], 2);
        }

        if ($startTime) {
            $peakInfo = $this->calculatePeakHourCost($field, $durationHours, $startTime);
            $breakdown['peak_hour_info'] = $peakInfo;
        }

        return $breakdown;
    }
}
