@extends('layouts.admin')

@section('title', 'Admin Dashboard - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Admin Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Admin Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-lg bg-primary-transparent">
                                <i class="ti ti-shield-check fs-18"></i>
                            </span>
                        </div>
                        <div>
                            <h5 class="fw-semibold mb-1">Welcome back, {{ auth()->user()->name ?? 'Admin' }}!</h5>
                            <p class="text-muted mb-0">You have full administrative access to the Field Management System.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row gy-4">
        <!-- Total Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-primary-transparent">
                                <i class="ti ti-users fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Total Users</h6>
                            <h4 class="fw-bold text-primary mb-0">{{ \App\Models\User::count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-danger-transparent">
                                <i class="ti ti-shield-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Administrators</h6>
                            <h4 class="fw-bold text-danger mb-0">{{ \App\Models\User::where('role', 'admin')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="ti ti-user fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Employees</h6>
                            <h4 class="fw-bold text-success mb-0">
                                {{ \App\Models\User::where('role', 'employee')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-warning-transparent">
                                <i class="ti ti-briefcase fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Users</h6>
                            <h4 class="fw-bold text-warning mb-0">{{ \App\Models\User::where('role', 'user')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Sections -->
    <div class="row gy-4">
        <!-- User Management Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">User Management</div>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-primary btn-sm">
                        <i class="ti ti-users me-1"></i>Manage All Users
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Comprehensive user management and role administration.</p>
                    <div class="row gy-2">
                        <div class="col-12">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="ti ti-list me-1"></i>View All Users
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('admin.users.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-user-plus me-1"></i>Create New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reservations Management Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations</div>
                    <a href="{{ route('reservations.index') }}" class="btn btn-info btn-sm">
                        <i class="ti ti-calendar-check me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage property reservations, bookings, and availability.</p>
                    <div class="row gy-2">
                        <div class="col-12">
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-info btn-sm w-100">
                                <i class="ti ti-list me-1"></i>Reservations
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('reservations.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-plus me-1"></i>New Reservation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Overview Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">System Overview</div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Quick overview of system status and recent activity.</p>
                    <div class="row gy-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="fw-bold text-primary mb-1">
                                    {{ \App\Models\User::whereDate('created_at', today())->count() }}</h5>
                                <p class="text-muted fs-12 mb-0">New Users Today</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="fw-bold text-success mb-1">
                                    {{ \App\Models\User::whereNotNull('email_verified_at')->count() }}</h5>
                                <p class="text-muted fs-12 mb-0">Verified Users</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Quick Actions</div>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary w-100">
                                <i class="ti ti-users me-2"></i>User Management
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('admin.fields.index') }}" class="btn btn-outline-success w-100">
                                <i class="ti ti-building me-2"></i>Field Management
                            </a>
                        </div>
                        <!--<div class="col-xl-3 col-lg-4 col-md-6">
                                <a href="{{ route('bookings.index') }}" class="btn btn-outline-info w-100">
                                    <i class="ti ti-calendar me-2"></i>Booking Management
                                </a>
                            </div>-->
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('profile.edit') }}" class="btn btn-outline-secondary w-100">
                                <i class="ti ti-settings me-2"></i>Profile Settings
                            </a>
                        </div>

                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('reservations.create') }}" class="btn btn-outline-primary w-100">
                                <i class="ti ti-plus me-1"></i>New Reservation
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <a href="{{ route('calendar.index') }}" class="btn btn-outline-info w-100">
                                <i class="ti ti-calendar me-1"></i>View Calendar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
